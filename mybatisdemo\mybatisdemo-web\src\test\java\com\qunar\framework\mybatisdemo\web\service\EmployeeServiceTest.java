package com.qunar.framework.mybatisdemo.web.service;

import com.qunar.framework.mybatisdemo.web.entity.Employee;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 员工服务测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EmployeeServiceTest {

    @Autowired
    private EmployeeService employeeService;

    /**
     * 测试保存新入职员工韩梅梅的信息
     * 员工工号为8，女士，其他自定义
     */
    @Test
    public void testAddHanMeiMei() {
        // 创建韩梅梅的员工信息
        Employee hanMeiMei = new Employee();
        hanMeiMei.setStaffId(8);                    // 员工工号：8
        hanMeiMei.setName("韩梅梅");                 // 姓名：韩梅梅
        hanMeiMei.setMobile("13800138008");         // 手机号码（自定义）
        hanMeiMei.setArea("北京");                   // 工作地区：北京（自定义）
        hanMeiMei.setGender(2);                     // 性别：2（女士）
        hanMeiMei.setIsValid(1);                    // 在职状态：1（在职）

        // 保存员工信息
        boolean success = employeeService.addEmployee(hanMeiMei);
        
        // 验证保存结果
        if (success) {
            System.out.println("✅ 韩梅梅员工信息保存成功！");
            System.out.println("员工信息：" + hanMeiMei);
            
            // 验证是否能查询到刚保存的员工信息
            Employee savedEmployee = employeeService.getEmployeeByStaffId(8);
            if (savedEmployee != null) {
                System.out.println("✅ 验证成功：能够查询到韩梅梅的员工信息");
                System.out.println("查询结果：" + savedEmployee);
            } else {
                System.out.println("❌ 验证失败：无法查询到韩梅梅的员工信息");
            }
        } else {
            System.out.println("❌ 韩梅梅员工信息保存失败！");
        }
    }

    /**
     * 测试查询所有员工信息（包括新添加的韩梅梅）
     */
    @Test
    public void testGetAllEmployeesIncludingHanMeiMei() {
        System.out.println("=== 所有员工信息列表 ===");
        employeeService.getAllEmployees().forEach(employee -> {
            System.out.println(employee);
        });
    }
}
