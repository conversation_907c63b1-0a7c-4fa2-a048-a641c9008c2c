package com.qunar.framework.mybatisdemo.web.controller;

import com.qunar.framework.mybatisdemo.web.entity.Employee;
import com.qunar.framework.mybatisdemo.web.service.EmployeeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工信息控制器
 */
@RestController
@RequestMapping("/api/employee")
public class EmployeeController {

    @Autowired
    private EmployeeService employeeService;

    /**
     * 根据ID查询员工信息
     * @param id 员工ID
     * @return 员工信息
     */
    @GetMapping("/{id}")
    public Employee getEmployeeById(@PathVariable Integer id) {
        return employeeService.getEmployeeById(id);
    }

    /**
     * 根据员工工号查询员工信息
     * @param staffId 员工工号
     * @return 员工信息
     */
    @GetMapping("/staff/{staffId}")
    public Employee getEmployeeByStaffId(@PathVariable Integer staffId) {
        return employeeService.getEmployeeByStaffId(staffId);
    }

    /**
     * 查询所有员工信息
     * @return 员工信息列表
     */
    @GetMapping("/all")
    public List<Employee> getAllEmployees() {
        return employeeService.getAllEmployees();
    }

    /**
     * 根据地区查询员工信息
     * @param area 工作地区
     * @return 员工信息列表
     */
    @GetMapping("/area/{area}")
    public List<Employee> getEmployeesByArea(@PathVariable String area) {
        return employeeService.getEmployeesByArea(area);
    }

    /**
     * 根据在职状态查询员工信息
     * @param isValid 在职状态 1:在职 2:离职
     * @return 员工信息列表
     */
    @GetMapping("/status/{isValid}")
    public List<Employee> getEmployeesByIsValid(@PathVariable Integer isValid) {
        return employeeService.getEmployeesByIsValid(isValid);
    }

    /**
     * 添加员工信息
     * @param employee 员工信息
     * @return 操作结果
     */
    @PostMapping
    public String addEmployee(@RequestBody Employee employee) {
        boolean success = employeeService.addEmployee(employee);
        return success ? "添加成功" : "添加失败";
    }

    /**
     * 更新员工信息
     * @param employee 员工信息
     * @return 操作结果
     */
    @PutMapping
    public String updateEmployee(@RequestBody Employee employee) {
        boolean success = employeeService.updateEmployee(employee);
        return success ? "更新成功" : "更新失败";
    }

    /**
     * 根据ID删除员工信息
     * @param id 员工ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public String deleteEmployeeById(@PathVariable Integer id) {
        boolean success = employeeService.deleteEmployeeById(id);
        return success ? "删除成功" : "删除失败";
    }

    /**
     * 添加韩梅梅员工信息的专用接口
     * @return 操作结果
     */
    @PostMapping("/add-hanmeimei")
    public String addHanMeiMei() {
        // 首先检查工号8是否已存在
        Employee existingEmployee = employeeService.getEmployeeByStaffId(8);
        if (existingEmployee != null) {
            return "工号8的员工已存在：" + existingEmployee.getName() + "，无法重复添加";
        }

        // 创建韩梅梅的员工信息
        Employee hanMeiMei = new Employee();
        hanMeiMei.setStaffId(8);                    // 员工工号：8
        hanMeiMei.setName("韩梅梅");                 // 姓名：韩梅梅
        hanMeiMei.setMobile("13800138008");         // 手机号码（自定义）
        hanMeiMei.setArea("北京");                   // 工作地区：北京（自定义）
        hanMeiMei.setGender(2);                     // 性别：2（女士）
        hanMeiMei.setIsValid(1);                    // 在职状态：1（在职）

        // 保存员工信息
        boolean success = employeeService.addEmployee(hanMeiMei);

        if (success) {
            return "✅ 韩梅梅员工信息保存成功！员工工号：8，姓名：韩梅梅，性别：女，工作地区：北京";
        } else {
            return "❌ 韩梅梅员工信息保存失败！";
        }
    }
}
