package com.qunar.framework.mybatisdemo.web.controller;

import com.qunar.framework.mybatisdemo.web.entity.LeaveHoliday;
import com.qunar.framework.mybatisdemo.web.service.LeaveHolidayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 员工请假控制器
 */
@RestController
@RequestMapping("/api/leave")
public class LeaveHolidayController {

    @Autowired
    private LeaveHolidayService leaveHolidayService;

    /**
     * 根据ID查询请假信息
     * @param id 请假ID
     * @return 请假信息
     */
    @GetMapping("/{id}")
    public LeaveHoliday getLeaveHolidayById(@PathVariable Integer id) {
        return leaveHolidayService.getLeaveHolidayById(id);
    }

    /**
     * 根据员工工号查询请假信息
     * @param staffId 员工工号
     * @return 请假信息列表
     */
    @GetMapping("/staff/{staffId}")
    public List<LeaveHoliday> getLeaveHolidaysByStaffId(@PathVariable Integer staffId) {
        return leaveHolidayService.getLeaveHolidaysByStaffId(staffId);
    }

    /**
     * 查询所有请假信息
     * @return 请假信息列表
     */
    @GetMapping("/all")
    public List<LeaveHoliday> getAllLeaveHolidays() {
        return leaveHolidayService.getAllLeaveHolidays();
    }

    /**
     * 根据地区查询请假信息
     * @param area 工作地区
     * @return 请假信息列表
     */
    @GetMapping("/area/{area}")
    public List<LeaveHoliday> getLeaveHolidaysByArea(@PathVariable String area) {
        return leaveHolidayService.getLeaveHolidaysByArea(area);
    }

    /**
     * 根据请假类型查询请假信息
     * @param type 请假类型 1:年假 2:病假
     * @return 请假信息列表
     */
    @GetMapping("/type/{type}")
    public List<LeaveHoliday> getLeaveHolidaysByType(@PathVariable Integer type) {
        return leaveHolidayService.getLeaveHolidaysByType(type);
    }

    /**
     * 根据时间范围查询请假信息
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 请假信息列表
     */
    @GetMapping("/date-range")
    public List<LeaveHoliday> getLeaveHolidaysByDateRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        return leaveHolidayService.getLeaveHolidaysByDateRange(startDate, endDate);
    }

    /**
     * 根据员工工号和时间范围查询请假信息
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 请假信息列表
     */
    @GetMapping("/staff/{staffId}/date-range")
    public List<LeaveHoliday> getLeaveHolidaysByStaffIdAndDateRange(
            @PathVariable Integer staffId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        return leaveHolidayService.getLeaveHolidaysByStaffIdAndDateRange(staffId, startDate, endDate);
    }

    /**
     * 添加请假信息
     * @param leaveHoliday 请假信息
     * @return 操作结果
     */
    @PostMapping
    public String addLeaveHoliday(@RequestBody LeaveHoliday leaveHoliday) {
        boolean success = leaveHolidayService.addLeaveHoliday(leaveHoliday);
        return success ? "添加成功" : "添加失败";
    }

    /**
     * 更新请假信息
     * @param leaveHoliday 请假信息
     * @return 操作结果
     */
    @PutMapping
    public String updateLeaveHoliday(@RequestBody LeaveHoliday leaveHoliday) {
        boolean success = leaveHolidayService.updateLeaveHoliday(leaveHoliday);
        return success ? "更新成功" : "更新失败";
    }

    /**
     * 根据ID删除请假信息
     * @param id 请假ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public String deleteLeaveHolidayById(@PathVariable Integer id) {
        boolean success = leaveHolidayService.deleteLeaveHolidayById(id);
        return success ? "删除成功" : "删除失败";
    }

    /**
     * 根据员工工号删除请假信息
     * @param staffId 员工工号
     * @return 操作结果
     */
    @DeleteMapping("/staff/{staffId}")
    public String deleteLeaveHolidayByStaffId(@PathVariable Integer staffId) {
        boolean success = leaveHolidayService.deleteLeaveHolidayByStaffId(staffId);
        return success ? "删除成功" : "删除失败";
    }

    /**
     * 申请请假（包含假期余额检查和扣减）
     * @param leaveHoliday 请假信息
     * @return 申请结果
     */
    @PostMapping("/apply")
    public String applyLeaveHoliday(@RequestBody LeaveHoliday leaveHoliday) {
        return leaveHolidayService.applyLeaveHoliday(leaveHoliday);
    }

    /**
     * 撤销请假（恢复假期余额）
     * @param id 请假ID
     * @return 撤销结果
     */
    @DeleteMapping("/{id}/cancel")
    public String cancelLeaveHoliday(@PathVariable Integer id) {
        return leaveHolidayService.cancelLeaveHoliday(id);
    }

    /**
     * 计算员工在指定时间范围内的请假总天数
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param type 请假类型（可选）
     * @return 请假总天数
     */
    @GetMapping("/staff/{staffId}/total-days")
    public String calculateTotalLeaveDays(
            @PathVariable Integer staffId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) Integer type) {
        int totalDays = leaveHolidayService.calculateTotalLeaveDays(staffId, startDate, endDate, type);
        String typeStr = type == null ? "所有类型" : (type == 1 ? "年假" : "病假");
        return String.format("员工%d在指定时间范围内的%s请假总天数为：%d天", staffId, typeStr, totalDays);
    }

    /**
     * 检查员工在指定时间段是否有请假冲突
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param excludeId 排除的请假ID（可选）
     * @return 检查结果
     */
    @GetMapping("/staff/{staffId}/conflict-check")
    public String checkLeaveConflict(
            @PathVariable Integer staffId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) Integer excludeId) {
        boolean hasConflict = leaveHolidayService.hasLeaveConflict(staffId, startDate, endDate, excludeId);
        return hasConflict ? 
            "指定时间段存在请假冲突，无法申请" : 
            "指定时间段无请假冲突，可以申请";
    }
}
