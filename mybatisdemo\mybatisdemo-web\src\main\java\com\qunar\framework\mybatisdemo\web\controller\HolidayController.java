package com.qunar.framework.mybatisdemo.web.controller;

import com.qunar.framework.mybatisdemo.web.entity.Holiday;
import com.qunar.framework.mybatisdemo.web.service.HolidayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工假期控制器
 */
@RestController
@RequestMapping("/api/holiday")
public class HolidayController {

    @Autowired
    private HolidayService holidayService;

    /**
     * 根据ID查询假期信息
     * @param id 假期ID
     * @return 假期信息
     */
    @GetMapping("/{id}")
    public Holiday getHolidayById(@PathVariable Integer id) {
        return holidayService.getHolidayById(id);
    }

    /**
     * 根据员工工号查询假期信息
     * @param staffId 员工工号
     * @return 假期信息
     */
    @GetMapping("/staff/{staffId}")
    public Holiday getHolidayByStaffId(@PathVariable Integer staffId) {
        return holidayService.getHolidayByStaffId(staffId);
    }

    /**
     * 查询所有假期信息
     * @return 假期信息列表
     */
    @GetMapping("/all")
    public List<Holiday> getAllHolidays() {
        return holidayService.getAllHolidays();
    }

    /**
     * 添加假期信息
     * @param holiday 假期信息
     * @return 操作结果
     */
    @PostMapping
    public String addHoliday(@RequestBody Holiday holiday) {
        boolean success = holidayService.addHoliday(holiday);
        return success ? "添加成功" : "添加失败";
    }

    /**
     * 更新假期信息
     * @param holiday 假期信息
     * @return 操作结果
     */
    @PutMapping
    public String updateHoliday(@RequestBody Holiday holiday) {
        boolean success = holidayService.updateHoliday(holiday);
        return success ? "更新成功" : "更新失败";
    }

    /**
     * 根据员工工号更新病假天数
     * @param staffId 员工工号
     * @param sickNum 病假天数
     * @return 操作结果
     */
    @PutMapping("/staff/{staffId}/sick/{sickNum}")
    public String updateSickNumByStaffId(@PathVariable Integer staffId, @PathVariable Integer sickNum) {
        boolean success = holidayService.updateSickNumByStaffId(staffId, sickNum);
        return success ? "病假天数更新成功" : "病假天数更新失败";
    }

    /**
     * 根据员工工号更新年假天数
     * @param staffId 员工工号
     * @param annualNum 年假天数
     * @return 操作结果
     */
    @PutMapping("/staff/{staffId}/annual/{annualNum}")
    public String updateAnnualNumByStaffId(@PathVariable Integer staffId, @PathVariable Integer annualNum) {
        boolean success = holidayService.updateAnnualNumByStaffId(staffId, annualNum);
        return success ? "年假天数更新成功" : "年假天数更新失败";
    }

    /**
     * 根据ID删除假期信息
     * @param id 假期ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public String deleteHolidayById(@PathVariable Integer id) {
        boolean success = holidayService.deleteHolidayById(id);
        return success ? "删除成功" : "删除失败";
    }

    /**
     * 根据员工工号删除假期信息
     * @param staffId 员工工号
     * @return 操作结果
     */
    @DeleteMapping("/staff/{staffId}")
    public String deleteHolidayByStaffId(@PathVariable Integer staffId) {
        boolean success = holidayService.deleteHolidayByStaffId(staffId);
        return success ? "删除成功" : "删除失败";
    }

    /**
     * 为员工初始化假期信息
     * @param staffId 员工工号
     * @param sickNum 病假天数（默认值）
     * @param annualNum 年假天数（默认值）
     * @return 操作结果
     */
    @PostMapping("/staff/{staffId}/init")
    public String initializeHolidayForEmployee(@PathVariable Integer staffId, 
                                             @RequestParam(defaultValue = "10") Integer sickNum,
                                             @RequestParam(defaultValue = "5") Integer annualNum) {
        boolean success = holidayService.initializeHolidayForEmployee(staffId, sickNum, annualNum);
        return success ? "假期信息初始化成功" : "假期信息初始化失败";
    }

    /**
     * 检查员工假期余额是否足够
     * @param staffId 员工工号
     * @param type 假期类型 1:年假 2:病假
     * @param requestDays 请求的天数
     * @return 检查结果
     */
    @GetMapping("/staff/{staffId}/check")
    public String checkHolidayBalance(@PathVariable Integer staffId, 
                                    @RequestParam Integer type, 
                                    @RequestParam Integer requestDays) {
        boolean hasEnough = holidayService.hasEnoughHolidayBalance(staffId, type, requestDays);
        String typeStr = type == 1 ? "年假" : "病假";
        return hasEnough ? 
            String.format("%s余额充足，可以请假%d天", typeStr, requestDays) : 
            String.format("%s余额不足，无法请假%d天", typeStr, requestDays);
    }

    /**
     * 扣减员工假期余额
     * @param staffId 员工工号
     * @param type 假期类型 1:年假 2:病假
     * @param days 扣减天数
     * @return 操作结果
     */
    @PutMapping("/staff/{staffId}/deduct")
    public String deductHolidayBalance(@PathVariable Integer staffId, 
                                     @RequestParam Integer type, 
                                     @RequestParam Integer days) {
        boolean success = holidayService.deductHolidayBalance(staffId, type, days);
        String typeStr = type == 1 ? "年假" : "病假";
        return success ? 
            String.format("%s余额扣减成功，扣减%d天", typeStr, days) : 
            String.format("%s余额扣减失败", typeStr);
    }

    /**
     * 恢复员工假期余额
     * @param staffId 员工工号
     * @param type 假期类型 1:年假 2:病假
     * @param days 恢复天数
     * @return 操作结果
     */
    @PutMapping("/staff/{staffId}/restore")
    public String restoreHolidayBalance(@PathVariable Integer staffId, 
                                      @RequestParam Integer type, 
                                      @RequestParam Integer days) {
        boolean success = holidayService.restoreHolidayBalance(staffId, type, days);
        String typeStr = type == 1 ? "年假" : "病假";
        return success ? 
            String.format("%s余额恢复成功，恢复%d天", typeStr, days) : 
            String.format("%s余额恢复失败", typeStr);
    }
}
