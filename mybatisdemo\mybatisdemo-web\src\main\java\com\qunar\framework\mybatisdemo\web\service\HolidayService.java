package com.qunar.framework.mybatisdemo.web.service;

import com.qunar.framework.mybatisdemo.web.entity.Holiday;
import com.qunar.framework.mybatisdemo.web.mapper.HolidayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工假期服务类
 */
@Service
public class HolidayService {

    @Autowired
    private HolidayMapper holidayMapper;

    /**
     * 根据ID查询假期信息
     * @param id 假期ID
     * @return 假期信息
     */
    public Holiday getHolidayById(Integer id) {
        return holidayMapper.selectById(id);
    }

    /**
     * 根据员工工号查询假期信息
     * @param staffId 员工工号
     * @return 假期信息
     */
    public Holiday getHolidayByStaffId(Integer staffId) {
        return holidayMapper.selectByStaffId(staffId);
    }

    /**
     * 查询所有假期信息
     * @return 假期信息列表
     */
    public List<Holiday> getAllHolidays() {
        return holidayMapper.selectAll();
    }

    /**
     * 添加假期信息
     * @param holiday 假期信息
     * @return 是否成功
     */
    public boolean addHoliday(Holiday holiday) {
        return holidayMapper.insert(holiday) > 0;
    }

    /**
     * 更新假期信息
     * @param holiday 假期信息
     * @return 是否成功
     */
    public boolean updateHoliday(Holiday holiday) {
        return holidayMapper.update(holiday) > 0;
    }

    /**
     * 根据员工工号更新病假天数
     * @param staffId 员工工号
     * @param sickNum 病假天数
     * @return 是否成功
     */
    public boolean updateSickNumByStaffId(Integer staffId, Integer sickNum) {
        return holidayMapper.updateSickNumByStaffId(staffId, sickNum) > 0;
    }

    /**
     * 根据员工工号更新年假天数
     * @param staffId 员工工号
     * @param annualNum 年假天数
     * @return 是否成功
     */
    public boolean updateAnnualNumByStaffId(Integer staffId, Integer annualNum) {
        return holidayMapper.updateAnnualNumByStaffId(staffId, annualNum) > 0;
    }

    /**
     * 根据ID删除假期信息
     * @param id 假期ID
     * @return 是否成功
     */
    public boolean deleteHolidayById(Integer id) {
        return holidayMapper.deleteById(id) > 0;
    }

    /**
     * 根据员工工号删除假期信息
     * @param staffId 员工工号
     * @return 是否成功
     */
    public boolean deleteHolidayByStaffId(Integer staffId) {
        return holidayMapper.deleteByStaffId(staffId) > 0;
    }

    /**
     * 初始化员工假期信息
     * 为新员工创建默认的假期配额
     * @param staffId 员工工号
     * @param sickNum 病假天数（默认值）
     * @param annualNum 年假天数（默认值）
     * @return 是否成功
     */
    public boolean initializeHolidayForEmployee(Integer staffId, Integer sickNum, Integer annualNum) {
        Holiday holiday = new Holiday(staffId, sickNum, annualNum);
        return addHoliday(holiday);
    }

    /**
     * 检查员工是否有足够的假期余额
     * @param staffId 员工工号
     * @param type 假期类型 1:年假 2:病假
     * @param requestDays 请求的天数
     * @return 是否有足够余额
     */
    public boolean hasEnoughHolidayBalance(Integer staffId, Integer type, Integer requestDays) {
        Holiday holiday = getHolidayByStaffId(staffId);
        if (holiday == null) {
            return false;
        }
        
        if (type == 1) { // 年假
            return holiday.getAnnualNum() >= requestDays;
        } else if (type == 2) { // 病假
            return holiday.getSickNum() >= requestDays;
        }
        
        return false;
    }

    /**
     * 扣减员工假期余额
     * @param staffId 员工工号
     * @param type 假期类型 1:年假 2:病假
     * @param days 扣减天数
     * @return 是否成功
     */
    public boolean deductHolidayBalance(Integer staffId, Integer type, Integer days) {
        Holiday holiday = getHolidayByStaffId(staffId);
        if (holiday == null) {
            return false;
        }
        
        if (type == 1) { // 年假
            int newAnnualNum = holiday.getAnnualNum() - days;
            if (newAnnualNum < 0) {
                return false;
            }
            return updateAnnualNumByStaffId(staffId, newAnnualNum);
        } else if (type == 2) { // 病假
            int newSickNum = holiday.getSickNum() - days;
            if (newSickNum < 0) {
                return false;
            }
            return updateSickNumByStaffId(staffId, newSickNum);
        }
        
        return false;
    }

    /**
     * 恢复员工假期余额（用于请假撤销等场景）
     * @param staffId 员工工号
     * @param type 假期类型 1:年假 2:病假
     * @param days 恢复天数
     * @return 是否成功
     */
    public boolean restoreHolidayBalance(Integer staffId, Integer type, Integer days) {
        Holiday holiday = getHolidayByStaffId(staffId);
        if (holiday == null) {
            return false;
        }
        
        if (type == 1) { // 年假
            int newAnnualNum = holiday.getAnnualNum() + days;
            return updateAnnualNumByStaffId(staffId, newAnnualNum);
        } else if (type == 2) { // 病假
            int newSickNum = holiday.getSickNum() + days;
            return updateSickNumByStaffId(staffId, newSickNum);
        }
        
        return false;
    }
}
