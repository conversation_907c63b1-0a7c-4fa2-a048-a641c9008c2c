package com.qunar.framework.mybatisdemo.web.service;

import com.qunar.framework.mybatisdemo.web.entity.LeaveHoliday;
import com.qunar.framework.mybatisdemo.web.mapper.LeaveHolidayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 员工请假服务类
 */
@Service
public class LeaveHolidayService {

    @Autowired
    private LeaveHolidayMapper leaveHolidayMapper;

    @Autowired
    private HolidayService holidayService;

    /**
     * 根据ID查询请假信息
     * @param id 请假ID
     * @return 请假信息
     */
    public LeaveHoliday getLeaveHolidayById(Integer id) {
        return leaveHolidayMapper.selectById(id);
    }

    /**
     * 根据员工工号查询请假信息
     * @param staffId 员工工号
     * @return 请假信息列表
     */
    public List<LeaveHoliday> getLeaveHolidaysByStaffId(Integer staffId) {
        return leaveHolidayMapper.selectByStaffId(staffId);
    }

    /**
     * 查询所有请假信息
     * @return 请假信息列表
     */
    public List<LeaveHoliday> getAllLeaveHolidays() {
        return leaveHolidayMapper.selectAll();
    }

    /**
     * 根据地区查询请假信息
     * @param area 工作地区
     * @return 请假信息列表
     */
    public List<LeaveHoliday> getLeaveHolidaysByArea(String area) {
        return leaveHolidayMapper.selectByArea(area);
    }

    /**
     * 根据请假类型查询请假信息
     * @param type 请假类型 1:年假 2:病假
     * @return 请假信息列表
     */
    public List<LeaveHoliday> getLeaveHolidaysByType(Integer type) {
        return leaveHolidayMapper.selectByType(type);
    }

    /**
     * 根据时间范围查询请假信息
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 请假信息列表
     */
    public List<LeaveHoliday> getLeaveHolidaysByDateRange(Date startDate, Date endDate) {
        return leaveHolidayMapper.selectByDateRange(startDate, endDate);
    }

    /**
     * 根据员工工号和时间范围查询请假信息
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 请假信息列表
     */
    public List<LeaveHoliday> getLeaveHolidaysByStaffIdAndDateRange(Integer staffId, Date startDate, Date endDate) {
        return leaveHolidayMapper.selectByStaffIdAndDateRange(staffId, startDate, endDate);
    }

    /**
     * 添加请假信息
     * @param leaveHoliday 请假信息
     * @return 是否成功
     */
    public boolean addLeaveHoliday(LeaveHoliday leaveHoliday) {
        return leaveHolidayMapper.insert(leaveHoliday) > 0;
    }

    /**
     * 更新请假信息
     * @param leaveHoliday 请假信息
     * @return 是否成功
     */
    public boolean updateLeaveHoliday(LeaveHoliday leaveHoliday) {
        return leaveHolidayMapper.update(leaveHoliday) > 0;
    }

    /**
     * 根据ID删除请假信息
     * @param id 请假ID
     * @return 是否成功
     */
    public boolean deleteLeaveHolidayById(Integer id) {
        return leaveHolidayMapper.deleteById(id) > 0;
    }

    /**
     * 根据员工工号删除请假信息
     * @param staffId 员工工号
     * @return 是否成功
     */
    public boolean deleteLeaveHolidayByStaffId(Integer staffId) {
        return leaveHolidayMapper.deleteByStaffId(staffId) > 0;
    }

    /**
     * 申请请假（包含假期余额检查和扣减）
     * @param leaveHoliday 请假信息
     * @return 申请结果信息
     */
    public String applyLeaveHoliday(LeaveHoliday leaveHoliday) {
        // 1. 检查假期余额是否足够
        boolean hasEnoughBalance = holidayService.hasEnoughHolidayBalance(
            leaveHoliday.getStaffId(), 
            leaveHoliday.getType(), 
            leaveHoliday.getDayNum()
        );
        
        if (!hasEnoughBalance) {
            return "假期余额不足，申请失败";
        }
        
        // 2. 扣减假期余额
        boolean deductSuccess = holidayService.deductHolidayBalance(
            leaveHoliday.getStaffId(), 
            leaveHoliday.getType(), 
            leaveHoliday.getDayNum()
        );
        
        if (!deductSuccess) {
            return "扣减假期余额失败，申请失败";
        }
        
        // 3. 保存请假记录
        boolean saveSuccess = addLeaveHoliday(leaveHoliday);
        
        if (!saveSuccess) {
            // 如果保存失败，需要恢复假期余额
            holidayService.restoreHolidayBalance(
                leaveHoliday.getStaffId(), 
                leaveHoliday.getType(), 
                leaveHoliday.getDayNum()
            );
            return "保存请假记录失败，申请失败";
        }
        
        return "请假申请成功";
    }

    /**
     * 撤销请假（恢复假期余额）
     * @param id 请假ID
     * @return 撤销结果信息
     */
    public String cancelLeaveHoliday(Integer id) {
        // 1. 获取请假信息
        LeaveHoliday leaveHoliday = getLeaveHolidayById(id);
        if (leaveHoliday == null) {
            return "请假记录不存在，撤销失败";
        }
        
        // 2. 恢复假期余额
        boolean restoreSuccess = holidayService.restoreHolidayBalance(
            leaveHoliday.getStaffId(), 
            leaveHoliday.getType(), 
            leaveHoliday.getDayNum()
        );
        
        if (!restoreSuccess) {
            return "恢复假期余额失败，撤销失败";
        }
        
        // 3. 删除请假记录
        boolean deleteSuccess = deleteLeaveHolidayById(id);
        
        if (!deleteSuccess) {
            // 如果删除失败，需要重新扣减假期余额
            holidayService.deductHolidayBalance(
                leaveHoliday.getStaffId(), 
                leaveHoliday.getType(), 
                leaveHoliday.getDayNum()
            );
            return "删除请假记录失败，撤销失败";
        }
        
        return "请假撤销成功";
    }

    /**
     * 计算员工在指定时间范围内的请假总天数
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param type 请假类型（可选，null表示所有类型）
     * @return 请假总天数
     */
    public int calculateTotalLeaveDays(Integer staffId, Date startDate, Date endDate, Integer type) {
        List<LeaveHoliday> leaveHolidays;
        
        if (type != null) {
            // 先按时间范围查询，再过滤类型
            leaveHolidays = getLeaveHolidaysByStaffIdAndDateRange(staffId, startDate, endDate);
            leaveHolidays = leaveHolidays.stream()
                .filter(leave -> leave.getType().equals(type))
                .collect(java.util.stream.Collectors.toList());
        } else {
            leaveHolidays = getLeaveHolidaysByStaffIdAndDateRange(staffId, startDate, endDate);
        }
        
        return leaveHolidays.stream()
            .mapToInt(LeaveHoliday::getDayNum)
            .sum();
    }

    /**
     * 检查员工在指定时间段是否有请假冲突
     * @param staffId 员工工号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param excludeId 排除的请假ID（用于更新时排除自己）
     * @return 是否有冲突
     */
    public boolean hasLeaveConflict(Integer staffId, Date startDate, Date endDate, Integer excludeId) {
        List<LeaveHoliday> existingLeaves = getLeaveHolidaysByStaffId(staffId);
        
        for (LeaveHoliday leave : existingLeaves) {
            // 排除指定的请假记录
            if (excludeId != null && leave.getId().equals(excludeId)) {
                continue;
            }
            
            // 检查时间是否重叠
            if (isDateRangeOverlap(startDate, endDate, leave.getStartDate(), leave.getEndDate())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查两个时间段是否重叠
     * @param start1 时间段1开始时间
     * @param end1 时间段1结束时间
     * @param start2 时间段2开始时间
     * @param end2 时间段2结束时间
     * @return 是否重叠
     */
    private boolean isDateRangeOverlap(Date start1, Date end1, Date start2, Date end2) {
        return start1.compareTo(end2) <= 0 && end1.compareTo(start2) >= 0;
    }
}
